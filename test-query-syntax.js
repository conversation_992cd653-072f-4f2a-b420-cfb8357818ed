const { Type, querySyntax } = require('@feathersjs/typebox');

// Test what querySyntax generates
const testSchema = Type.Object({
  _id: Type.String(),
  name: Type.String(),
  version: Type.String()
}, { additionalProperties: true });

const querySchema = querySyntax(testSchema);

console.log('Generated Query Schema:');
console.log(JSON.stringify(querySchema, null, 2));

// Check specifically what $sort looks like
if (querySchema.properties && querySchema.properties.$sort) {
  console.log('\n$sort schema:');
  console.log(JSON.stringify(querySchema.properties.$sort, null, 2));
}
