import { Type, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import { stateIds } from './states.js'
import { AnyObj } from 'feathers-ucan'

// Nullable utility types
export const Nullable = {
  String: Type.Union([Type.String(), Type.Null()]),
  Boolean: Type.Union([Type.Boolean(), Type.Null()])
}

// Simplified nullable helper
export const IsNullable = (type: any) => Type.Union([Type.Null(), type])

// RRule schema for recurring events
export const RRuleSchema = Type.Object({
  freq: Type.Optional(Type.String()),
  dtstart: Type.String(),
  until: Type.Optional(Type.String()),
  interval: Type.Optional(Type.Number()),
  count: Type.Optional(Type.Number()),
  bymonth: Type.Optional(Type.Array(Type.Number())),
  byweekday: Type.Optional(Type.Array(Type.String())),
  byhour: Type.Optional(Type.Array(Type.Number())),
  byminute: Type.Optional(Type.Array(Type.Number())),
  bysecond: Type.Optional(Type.Array(Type.Number())),
  bymonthday: Type.Optional(Type.Array(Type.Number())),
  byyearday: Type.Optional(Type.Array(Type.Number())),
  byweekno: Type.Optional(Type.Array(Type.Number())),
  bysetpos: Type.Optional(Type.Array(Type.Number())),
  wkst: Type.Optional(Type.String()),
  tzid: Type.Optional(Type.String())
}, { additionalProperties: false })

export type RRule = Static<typeof RRuleSchema>

// Mandate schema for legal agreements
export const MandateSchema = Type.Object({
  acceptedAt: Type.Optional(Type.Any()),
  ip: Type.Optional(Type.String()),
  ua: Type.Optional(Type.String()),
  copy: Type.Optional(Type.String()),
  fingerprint: ObjectIdSchema(),
  login: ObjectIdSchema(),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(Type.String()),
  signature: Type.Optional(Type.String())
}, { additionalProperties: false })

// Phone number schema
export const PhoneSchema = Type.Object({
  number: Type.Optional(Type.Object({
    input: Type.Optional(Type.String()),
    international: Type.Optional(Type.String()),
    national: Type.Optional(Type.String()),
    e164: Type.Optional(Type.String()),
    rfc3966: Type.Optional(Type.String()),
    significant: Type.Optional(Type.String())
  }, { additionalProperties: false })),
  regionCode: Type.Optional(Type.String()),
  valid: Type.Optional(Type.Boolean()),
  possible: Type.Optional(Type.Boolean()),
  possibility: Type.Optional(Type.String()),
  countryCode: Type.Optional(Type.Number()),
  canBeInternationallyDialled: Type.Optional(Type.Boolean()),
  typeIsMobile: Type.Optional(Type.Boolean()),
  typeIsFixedLine: Type.Optional(Type.Boolean())
}, { additionalProperties: true })

export type Phone = Static<typeof PhoneSchema>

// GeoJSON schemas
export const GeoJsonFeatureSchema = Type.Object({
  name: Type.Optional(Type.String()),
  type: Type.Optional(Type.Literal('Feature')),
  geometry: Type.Optional(Type.Object({
    type: Type.Optional(Type.String()),
    coordinates: Type.Optional(Type.Array(Type.Any()))
  }, { additionalProperties: true })),
  properties: Type.Optional(Type.Record(Type.String(), Type.Any())),
  addresses: Type.Optional(Type.Array(Type.Any()))
}, { additionalProperties: true })

export type GeoJsonFeature = Static<typeof GeoJsonFeatureSchema>

export const GeoJsonSchema = Type.Object({
  type: Type.Optional(Type.String()),
  allowFeatures: Type.Optional(Type.Array(Type.String())),
  features: Type.Optional(Type.Array(GeoJsonFeatureSchema))
}, { additionalProperties: true })

export type GeoJson = Static<typeof GeoJsonSchema>

// Image/File upload schema
export const ImageSchema = Type.Object({
  uploadId: ObjectIdSchema(),
  fileId: Type.Optional(Type.String()),
  storage: Type.Optional(Type.String()),
  appPath: Type.Optional(Nullable.Boolean),
  info: Type.Optional(Type.Object({
    name: Type.Optional(Type.String()),
    size: Type.Optional(Type.Number()),
    type: Type.Optional(Type.String()),
    lastModifiedDate: Type.Optional(Type.Any())
  }, { additionalProperties: false })),
  subPath: Type.Optional(Type.Union([Type.Array(Type.String()), Type.Null()])),
  url: Type.Optional(Type.String())
}, { additionalProperties: true })

export type Image = Static<typeof ImageSchema>

// Video schema extends image schema
export const VideoSchema = Type.Intersect([
  ImageSchema,
  Type.Object({
    title: Type.Optional(Type.String()),
    author_name: Type.Optional(Type.String()),
    author_url: Type.Optional(Type.String()),
    type: Type.Optional(Type.String()),
    height: Type.Optional(Type.Any()),
    width: Type.Optional(Type.Any()),
    version: Type.Optional(Type.Any()),
    provider_name: Type.Optional(Type.String()),
    provider_url: Type.Optional(Type.String()),
    thumbnail_height: Type.Optional(Type.Number()),
    thumbnail_width: Type.Optional(Type.Number()),
    thumbnail_url: Type.Optional(Type.String())
  })
])

// Updates tracking schema
export const UpdatesSchema = Type.Object({
  did: Type.Optional(Nullable.String),
  login: Type.Union([ObjectIdSchema(), Type.Null(), Type.String()]),
  fingerprint: Type.Union([ObjectIdSchema(), Type.Null(), Type.String()]),
  origin: Type.Optional(Nullable.String),
  longtail: Type.Optional(Nullable.String),
  at: Type.Optional(Type.Any())
}, { additionalProperties: true })

export type Updates = Static<typeof UpdatesSchema>

const UpdatedByHistoryEntrySchema = Type.Intersect([
  UpdatesSchema,
  Type.Object({ updatedAt: Type.Optional(Type.Any()) })
])

const UpdatedByHistorySchema = Type.Array(UpdatedByHistoryEntrySchema)

// Common fields that appear in all entities - structured for both spreading and Pick operations
const commonFieldsProperties = {
  env: Type.Optional(ObjectIdSchema()),
  host: Type.Optional(ObjectIdSchema()),
  ref: Type.Optional(ObjectIdSchema()),
  changeLog: Type.Optional(ObjectIdSchema()),
  editMap: Type.Optional(Type.Record(Type.String(), Type.Any())),
  deleted: Type.Optional(Type.Boolean()),
  session_fp: Type.Optional(Type.String()),
  deletedAt: Type.Optional(Type.Any()),
  updatedAt: Type.Optional(Type.Any()),
  createdAt: Type.Optional(Type.Any()),
  createdBy: Type.Optional(UpdatesSchema),
  updatedBy: Type.Optional(UpdatesSchema),
  updatedByHistory: Type.Optional(UpdatedByHistorySchema)
}

import { queryWrapper } from '@feathersjs/typebox';

export const queryWrapper = (schema:any, extensions?:any, options?:any) => {
  return queryWrapper(schema, extensions, { additionalProperties: true, ...options })
}
// For spreading into schemas
export const commonFields = commonFieldsProperties

// For Pick operations in query schemas
export const CommonFieldsSchema = Type.Object(commonFieldsProperties, { additionalProperties: true })
export type CommonFields = Static<typeof CommonFieldsSchema>

// Address schema
export const AddressSchema = Type.Object({
  id: Type.Optional(Type.String()),
  address1: Type.Optional(Type.String()),
  address2: Type.Optional(Type.String()),
  formatted: Type.Optional(Type.String()),
  postal: Type.Optional(Type.String()),
  city: Type.Optional(Type.String()),
  region: Type.Optional(Type.String()),
  country: Type.Optional(Type.String()),
  latitude: Type.Optional(Type.Number()),
  longitude: Type.Optional(Type.Number()),
  googleAddress: Type.Optional(Type.Record(Type.String(), Type.Any())),
  name: Type.Optional(Type.String()),
  tags: Type.Optional(Type.Record(Type.String(), Type.Any())),
  type: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: true })

export const ServiceAddressSchema = Type.Intersect([
  AddressSchema,
  Type.Object({
    geo: Type.Optional(GeoJsonSchema)
  })
])

// Utility for "effectively any" type
export const EffectiveAny = Type.Union([
  Type.String(),
  Type.Record(Type.String(), Type.Any()),
  Type.Array(Type.Any()),
  Type.Number()
])

// Tax-related schemas
const TaxItemSchema = Type.Object({
  name: Type.Optional(Type.String()),
  type: Type.Optional(Type.Union([Type.Literal('percent'), Type.Literal('flat')])),
  rate: Type.Optional(Type.Number()),
  notes: Type.Optional(Type.String())
}, { additionalProperties: false })

export const TaxSchema = Type.Object({
  automateTaxes: Type.Optional(Type.Boolean()),
  taxExempt: Type.Optional(Type.Boolean()),
  origin: Type.Optional(Type.Boolean()),
  taxOverrides: Type.Optional(Type.Array(TaxItemSchema)),
  taxes: Type.Optional(Type.Array(Type.Object({
    name: Type.Optional(Type.String()),
    isDefault: Type.Optional(Type.Boolean()),
    origin: Type.Optional(Type.Boolean()),
    areaId: Type.Optional(Type.String()),
    states: Type.Optional(Type.Array(Type.Union(stateIds.map(id => Type.Literal(id))))),
    postal_codes: Type.Optional(Type.Array(Type.String())),
    cities: Type.Optional(Type.Array(Type.String())),
    taxes: Type.Optional(Type.Array(TaxItemSchema))
  }, { additionalProperties: false })))
}, { additionalProperties: false })

// Common query properties
export const CommonQueriesSchema = Type.Object({
  $select: Type.Optional(Type.Any()),
  $regex: Type.Optional(Type.Any()),
  $text: Type.Optional(Type.Any()),
  $options: Type.Optional(Type.Any()),
  $search: Type.Optional(Type.Any()),
  $elemMatch: Type.Optional(Type.Any()),
  _limit_to: Type.Optional(Type.Any()),
  'updatedBy.login': ObjectIdSchema(),
  'createdBy.login': ObjectIdSchema()
}, { additionalProperties: true })

// ID list query schema
export const IdListQuerySchema = Type.Union([
  ObjectIdSchema(),
  Type.Array(ObjectIdSchema())
])

// Utility functions for creating TypeBox schemas

// Simplified patch schema helper - will be expanded later
export const CreateBasicPatchOps = () => {
  return {
    $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
    $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
    $push: Type.Optional(Type.Record(Type.String(), Type.Any()))
  }
}

// Helper to create $push/$addToSet operations
export const CreateAddToSetSchema = (opts: Array<{ path: string, type: any }>) => {
  const properties: Record<string, any> = {}

  // Always include updatedByHistory
  const allOpts = [
    { path: 'updatedByHistory', type: UpdatedByHistoryEntrySchema },
    ...opts
  ]

  allOpts.forEach(opt => {
    properties[opt.path] = Type.Union([
      opt.type,
      Type.Object({
        $position: Type.Optional(Type.Number()),
        $each: Type.Optional(Type.Array(opt.type)),
        value: Type.Optional(opt.type)
      }, { additionalProperties: true })
    ])
  })

  return Type.Object(properties, { additionalProperties: false })
}

// Simplified query schema helper - use queryWrapper from @feathersjs/typebox instead
export const CreateBasicQueryOps = () => {
  return {
    $limit: Type.Optional(Type.Number()),
    $skip: Type.Optional(Type.Number()),
    $sort: Type.Optional(Type.Record(Type.String(), Type.Union([Type.Literal(1), Type.Literal(-1)]))),
    $select: Type.Optional(Type.Array(Type.String()))
  }
}

// Utility to convert TypeBox schema to JSON Schema for OpenAI API usage
export const typeboxToJsonSchema = (typeboxSchema: any): any => {
  // This is a simplified converter - TypeBox schemas can be used directly as JSON Schema
  // in many cases, but we need to extract the properties for OpenAI
  if (typeboxSchema && typeboxSchema.properties) {
    return {
      type: 'object',
      properties: typeboxSchema.properties,
      additionalProperties: typeboxSchema.additionalProperties || false
    }
  }
  return typeboxSchema
}

// Default resolver helper
export const DefResolver = (defaultValue: any, falseyVals?: Array<any>) => {
  return async (val: any) => {
    if (val || (falseyVals || []).includes(val)) return val
    return defaultValue
  }
}

// Email validation and handling
export const IsEmail = (str: string): boolean => {
  return /^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/.test(str)
}

type EmailHandlerOptions = { throw?: boolean }
export const EmailHandler = (options?: EmailHandlerOptions) => {
  return async (val: string) => {
    if (val) {
      if (!IsEmail(val) && options?.throw) {
        throw new Error(`Invalid email - ${val}`)
      } else {
        return val.toLowerCase().trim()
      }
    }
    return val
  }
}

type TrimHandlerOptions = { lowercase?: boolean }
export const TrimHandler = ({ lowercase }: TrimHandlerOptions) => {
  return async (val: string) => {
    if (lowercase) val = val?.toLowerCase()
    if (val) return val.trim()
    return val
  }
}

export const EmailMapHandler = (options?: EmailHandlerOptions) => {
  return async (val: string[]) => {
    if (val) return await Promise.all(val.map(a => EmailHandler(options)(a)))
    return val
  }
}

// Helper to create exists queries
export const CreateExistsQuery = (paths: string[], properties: Record<string, any>) => {
  const props: Record<string, any> = {}
  for (const path of paths) {
    props[path] = Type.Union([
      properties[path],
      Type.Object({
        $exists: Type.Boolean()
      }, { additionalProperties: false })
    ])
  }
  return props
}

// Common patch function - equivalent to the original commonPatch
export const commonPatch = (properties: Record<string, any>, pushPullOpts: Array<{ path: string, type: any }> = []) => {
  return {
    type: 'object',
    additionalProperties: false,
    properties: {
      $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
      $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
      $push: Type.Optional(CreateAddToSetSchema([
        { path: 'updatedByHistory', type: UpdatedByHistoryEntrySchema },
        ...pushPullOpts
      ]))
    }
  }
}

// TypeBox addToSet function - supports $push and $addToSet operations
export const addToSet = (opts: Array<{ path: string, type: any }>) => {
  const properties: Record<string, any> = {}

  // Always include updatedByHistory
  const allOpts = [
    { path: 'updatedByHistory', type: UpdatedByHistoryEntrySchema },
    ...opts
  ]

  // If only updatedByHistory, return generic record
  if (allOpts.length === 1) {
    return Type.Record(Type.String(), Type.Any())
  }

  allOpts.forEach(opt => {
    properties[opt.path] = Type.Union([
      opt.type,
      Type.Object({
        $position: Type.Optional(Type.Number()),
        $each: Type.Optional(Type.Array(opt.type)),
        value: Type.Optional(opt.type)
      }, { additionalProperties: true })
    ])
  })

  return Type.Object(properties, { additionalProperties: false })
}

// TypeBox pull function - supports $pull operations
export const pull = (opts: Array<{ path: string, type: any }>) => {
  const properties: Record<string, any> = {}

  // If no options provided, return generic record
  if (opts.length === 0) {
    return Type.Record(Type.String(), Type.Any())
  }

  opts.forEach(opt => {
    properties[opt.path] = opt.type
  })

  return Type.Object(properties, { additionalProperties: true })
}
