// TypeBox schema for ai-chats service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

// Chat history item schema
const ChatHistoryItemSchema = Type.Object({
  session: Type.Optional(Type.String()),
  createdAt: Type.Optional(Type.Any()),
  subject: Type.Optional(Type.Union([
    Type.Literal('plan_docs'),
    Type.Literal('contracts'),
    Type.Literal('coverages'),
    Type.Literal('medical'),
    Type.Literal('shops')
  ])),
  question: Type.Optional(Type.String()),
  annotations: Type.Optional(Type.Array(Type.Any())),
  answer: Type.Optional(Type.String())
}, { additionalProperties: false })

// Main data model schema
export const aiChatsSchema = Type.Object({
  _id: ObjectIdSchema(),
  subject: ObjectIdSchema(),
  chatName: Type.String(),
  chatId: Type.Optional(Type.String()),
  person: ObjectIdSchema(),
  chats: Type.Optional(Type.Array(ChatHistoryItemSchema)),
  ...commonFields
}, { additionalProperties: false })

export type AiChats = Static<typeof aiChatsSchema>
export const aiChatsValidator = getValidator(aiChatsSchema, dataValidator)
export const aiChatsResolver = resolve<AiChats, HookContext>({})
export const aiChatsExternalResolver = resolve<AiChats, HookContext>({})

// Schema for creating new data
export const aiChatsDataSchema = Type.Object({
  ...Type.Omit(aiChatsSchema, ['_id']).properties
}, { additionalProperties: false })

export type AiChatsData = Static<typeof aiChatsDataSchema>
export const aiChatsDataValidator = getValidator(aiChatsDataSchema, dataValidator)
export const aiChatsDataResolver = resolve<AiChatsData, HookContext>({})

// Schema for updating existing data
export const aiChatsPatchSchema = Type.Object({
  subject: Type.Optional(ObjectIdSchema()),
  chatName: Type.Optional(Type.String()),
  chatId: Type.Optional(Type.String()),
  person: Type.Optional(ObjectIdSchema()),
  chats: Type.Optional(Type.Array(ChatHistoryItemSchema)),
  $push: Type.Optional(Type.Object({
    chats: Type.Optional(Type.Union([
      ChatHistoryItemSchema,
      Type.Object({
        $each: Type.Optional(Type.Array(ChatHistoryItemSchema))
      }, { additionalProperties: false })
    ]))
  }, { additionalProperties: false })),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
}, { additionalProperties: false })

export type AiChatsPatch = Static<typeof aiChatsPatchSchema>
export const aiChatsPatchValidator = getValidator(aiChatsPatchSchema, dataValidator)
export const aiChatsPatchResolver = resolve<AiChatsPatch, HookContext>({})

// Schema for allowed query properties
const aiChatsQueryProperties = Type.Pick(aiChatsSchema, ['_id', 'subject', 'chatName', 'chatId', 'person'], {
  additionalProperties: false
})
export const aiChatsQuerySchema = querySyntax(aiChatsQueryProperties)

export type AiChatsQuery = Static<typeof aiChatsQuerySchema>
export const aiChatsQueryValidator = getValidator(aiChatsQuerySchema, queryValidator)
export const aiChatsQueryResolver = resolve<AiChatsQuery, HookContext>({})
