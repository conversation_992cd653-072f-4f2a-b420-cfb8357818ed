// TypeBox schema for funds service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const fundsSchema = Type.Object({
  _id: ObjectIdSchema(),
  account: ObjectIdSchema(),
  amount: Type.Number(),
  source: Type.Optional(Type.String()),
  purpose: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  date: Type.Optional(Type.Any()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  reference: Type.Optional(ObjectIdSchema()),
  allocated: Type.Optional(Type.Boolean()),
  allocatedTo: Type.Optional(Type.Array(ObjectIdSchema())),
  restrictions: Type.Optional(Type.Array(Type.String())),
  expiresAt: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  name: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Funds = Static<typeof fundsSchema>
export const fundsValidator = getValidator(fundsSchema, dataValidator)
export const fundsResolver = resolve<Funds, HookContext>({})
export const fundsExternalResolver = resolve<Funds, HookContext>({})

export const fundsDataSchema = Type.Object({
  ...Type.Omit(fundsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FundsData = Static<typeof fundsDataSchema>
export const fundsDataValidator = getValidator(fundsDataSchema, dataValidator)
export const fundsDataResolver = resolve<FundsData, HookContext>({})

export const fundsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fundsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FundsPatch = Static<typeof fundsPatchSchema>
export const fundsPatchValidator = getValidator(fundsPatchSchema, dataValidator)
export const fundsPatchResolver = resolve<FundsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const fundsQueryProperties = Type.Object({
  ...Type.Pick(fundsSchema, ['_id', 'account', 'reference', 'allocatedTo', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const fundsQuerySchema = querySyntax(fundsQueryProperties)
export type FundsQuery = Static<typeof fundsQuerySchema>
export const fundsQueryValidator = getValidator(fundsQuerySchema, queryValidator)
export const fundsQueryResolver = resolve<FundsQuery, HookContext>({})
