// TypeBox schema for fbs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const fbsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Fbs = Static<typeof fbsSchema>
export const fbsValidator = getValidator(fbsSchema, dataValidator)
export const fbsResolver = resolve<Fbs, HookContext>({})
export const fbsExternalResolver = resolve<Fbs, HookContext>({})

export const fbsDataSchema = Type.Object({
  ...Type.Omit(fbsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FbsData = Static<typeof fbsDataSchema>
export const fbsDataValidator = getValidator(fbsDataSchema, dataValidator)
export const fbsDataResolver = resolve<FbsData, HookContext>({})

export const fbsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fbsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type FbsPatch = Static<typeof fbsPatchSchema>
export const fbsPatchValidator = getValidator(fbsPatchSchema, dataValidator)
export const fbsPatchResolver = resolve<FbsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const fbsQueryProperties = Type.Object({
  ...Type.Pick(fbsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const fbsQuerySchema = queryWrapper(fbsQueryProperties)
export type FbsQuery = Static<typeof fbsQuerySchema>
export const fbsQueryValidator = getValidator(fbsQuerySchema, queryValidator)
export const fbsQueryResolver = resolve<FbsQuery, HookContext>({})
