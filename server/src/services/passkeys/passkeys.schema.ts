// TypeBox schema for passkeys service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const passkeysSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  login: Type.Optional(Type.String()),
  rpID: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Passkeys = Static<typeof passkeysSchema>
export const passkeysValidator = getValidator(passkeysSchema, dataValidator)
export const passkeysResolver = resolve<Passkeys, HookContext>({})
export const passkeysExternalResolver = resolve<Passkeys, HookContext>({})

export const passkeysDataSchema = Type.Object({
  ...Type.Omit(passkeysSchema, ['_id']).properties
}, { additionalProperties: false })

export type PasskeysData = Static<typeof passkeysDataSchema>
export const passkeysDataValidator = getValidator(passkeysDataSchema, dataValidator)
export const passkeysDataResolver = resolve<PasskeysData, HookContext>({})

export const passkeysPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(passkeysSchema, ['_id'])).properties
}, { additionalProperties: false })
export type PasskeysPatch = Static<typeof passkeysPatchSchema>
export const passkeysPatchValidator = getValidator(passkeysPatchSchema, dataValidator)
export const passkeysPatchResolver = resolve<PasskeysPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const passkeysQueryProperties = Type.Object({
  ...Type.Pick(passkeysSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const passkeysQuerySchema = queryWrapper(passkeysQueryProperties)
export type PasskeysQuery = Static<typeof passkeysQuerySchema>
export const passkeysQueryValidator = getValidator(passkeysQuerySchema, queryValidator)
export const passkeysQueryResolver = resolve<PasskeysQuery, HookContext>({})
