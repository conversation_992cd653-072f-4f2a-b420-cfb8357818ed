// TypeBox schema for networks service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const networksSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  facilities: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  serviceArea: Type.Optional(Type.Array(ServiceAddressSchema)),
  tier: Type.Optional(Type.String()),
  contractNumber: Type.Optional(Type.String()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  avatar: Type.Optional(Type.String()),
  access: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Networks = Static<typeof networksSchema>
export const networksValidator = getValidator(networksSchema, dataValidator)
export const networksResolver = resolve<Networks, HookContext>({})
export const networksExternalResolver = resolve<Networks, HookContext>({})

export const networksDataSchema = Type.Object({
  ...Type.Omit(networksSchema, ['_id']).properties
}, { additionalProperties: false })

export type NetworksData = Static<typeof networksDataSchema>
export const networksDataValidator = getValidator(networksDataSchema, dataValidator)
export const networksDataResolver = resolve<NetworksData, HookContext>({})

export const networksPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(networksSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type NetworksPatch = Static<typeof networksPatchSchema>
export const networksPatchValidator = getValidator(networksPatchSchema, dataValidator)
export const networksPatchResolver = resolve<NetworksPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const networksQueryProperties = Type.Object({
  ...Type.Pick(networksSchema, ['_id', 'org', 'providers', 'facilities', 'practitioners', 'plans', 'coverages', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const networksQuerySchema = querySyntax(networksQueryProperties)
export type NetworksQuery = Static<typeof networksQuerySchema>
export const networksQueryValidator = getValidator(networksQuerySchema, queryValidator)
export const networksQueryResolver = resolve<NetworksQuery, HookContext>({})
