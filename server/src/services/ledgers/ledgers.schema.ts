// TypeBox schema for ledgers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const ledgersSchema = Type.Object({
  _id: ObjectIdSchema(),
  account: ObjectIdSchema(),
  amount: Type.Number(),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  reference: Type.Optional(ObjectIdSchema()),
  referenceType: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  date: Type.Optional(Type.Any()),
  balance: Type.Optional(Type.Number()),
  runningBalance: Type.Optional(Type.Number()),
  reconciled: Type.Optional(Type.Boolean()),
  reconciledAt: Type.Optional(Type.Any()),
  tags: Type.Optional(Type.Array(Type.String())),
  metadata: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  plan: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  planYear: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Ledgers = Static<typeof ledgersSchema>
export const ledgersValidator = getValidator(ledgersSchema, dataValidator)
export const ledgersResolver = resolve<Ledgers, HookContext>({})
export const ledgersExternalResolver = resolve<Ledgers, HookContext>({})

export const ledgersDataSchema = Type.Object({
  ...Type.Omit(ledgersSchema, ['_id']).properties
}, { additionalProperties: false })

export type LedgersData = Static<typeof ledgersDataSchema>
export const ledgersDataValidator = getValidator(ledgersDataSchema, dataValidator)
export const ledgersDataResolver = resolve<LedgersData, HookContext>({})

export const ledgersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(ledgersSchema, ['_id'])).properties
}, { additionalProperties: false })
export type LedgersPatch = Static<typeof ledgersPatchSchema>
export const ledgersPatchValidator = getValidator(ledgersPatchSchema, dataValidator)
export const ledgersPatchResolver = resolve<LedgersPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const ledgersQueryProperties = Type.Object({
  ...Type.Pick(ledgersSchema, ['_id', 'account', 'reference', 'plan', 'org', 'person', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const ledgersQuerySchema = queryWrapper(ledgersQueryProperties)
export type LedgersQuery = Static<typeof ledgersQuerySchema>
export const ledgersQueryValidator = getValidator(ledgersQuerySchema, queryValidator)
export const ledgersQueryResolver = resolve<LedgersQuery, HookContext>({})
