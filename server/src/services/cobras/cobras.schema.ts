// TypeBox schema for cobras service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const cobrasSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  enrollment: Type.Optional(ObjectIdSchema()),
  participant: Type.Optional(Type.String()),
  household: Type.Optional(ObjectIdSchema()),
  spec: Type.Optional(Type.String()),
  event_type: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Cobras = Static<typeof cobrasSchema>
export const cobrasValidator = getValidator(cobrasSchema, dataValidator)
export const cobrasResolver = resolve<Cobras, HookContext>({})
export const cobrasExternalResolver = resolve<Cobras, HookContext>({})

export const cobrasDataSchema = Type.Object({
  ...Type.Omit(cobrasSchema, ['_id']).properties
}, { additionalProperties: false })

export type CobrasData = Static<typeof cobrasDataSchema>
export const cobrasDataValidator = getValidator(cobrasDataSchema, dataValidator)
export const cobrasDataResolver = resolve<CobrasData, HookContext>({})

export const cobrasPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(cobrasSchema, ['_id'])).properties
}, { additionalProperties: false })
export type CobrasPatch = Static<typeof cobrasPatchSchema>
export const cobrasPatchValidator = getValidator(cobrasPatchSchema, dataValidator)
export const cobrasPatchResolver = resolve<CobrasPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const cobrasQueryProperties = Type.Object({
  ...Type.Pick(cobrasSchema, ['_id', 'enrollment', 'household', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const cobrasQuerySchema = querySyntax(cobrasQueryProperties)
export type CobrasQuery = Static<typeof cobrasQuerySchema>
export const cobrasQueryValidator = getValidator(cobrasQuerySchema, queryValidator)
export const cobrasQueryResolver = resolve<CobrasQuery, HookContext>({})
