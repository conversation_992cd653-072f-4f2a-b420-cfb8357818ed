// TypeBox schema for price-estimates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const priceEstimatesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  alts: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type PriceEstimates = Static<typeof priceEstimatesSchema>
export const priceEstimatesValidator = getValidator(priceEstimatesSchema, dataValidator)
export const priceEstimatesResolver = resolve<PriceEstimates, HookContext>({})
export const priceEstimatesExternalResolver = resolve<PriceEstimates, HookContext>({})

export const priceEstimatesDataSchema = Type.Object({
  ...Type.Omit(priceEstimatesSchema, ['_id']).properties
}, { additionalProperties: false })

export type PriceEstimatesData = Static<typeof priceEstimatesDataSchema>
export const priceEstimatesDataValidator = getValidator(priceEstimatesDataSchema, dataValidator)
export const priceEstimatesDataResolver = resolve<PriceEstimatesData, HookContext>({})

export const priceEstimatesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(priceEstimatesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type PriceEstimatesPatch = Static<typeof priceEstimatesPatchSchema>
export const priceEstimatesPatchValidator = getValidator(priceEstimatesPatchSchema, dataValidator)
export const priceEstimatesPatchResolver = resolve<PriceEstimatesPatch, HookContext>({})

// Allow querying on any field from the main schema
const priceEstimatesQueryProperties = priceEstimatesSchema
export const priceEstimatesQuerySchema = queryWrapper(priceEstimatesQueryProperties)
export type PriceEstimatesQuery = Static<typeof priceEstimatesQuerySchema>
export const priceEstimatesQueryValidator = getValidator(priceEstimatesQuerySchema, queryValidator)
export const priceEstimatesQueryResolver = resolve<PriceEstimatesQuery, HookContext>({})

// Export for backward compatibility with business logic
export const estimateBodySchema = Type.Object({
  procedure: Type.Optional(Type.String()),
  provider: Type.Optional(Type.String()),
  location: Type.Optional(Type.String()),
  insurance: Type.Optional(Type.String())
}, { additionalProperties: false })

export const priceKeys = Type.Object({
  amount: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number())
}, { additionalProperties: false })
