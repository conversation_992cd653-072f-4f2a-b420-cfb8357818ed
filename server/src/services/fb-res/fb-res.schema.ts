// TypeBox schema for fb-res service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const fbResSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  person: Type.Optional(ObjectIdSchema()),
  form: Type.Optional(Type.String()),
  formData: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type FbRes = Static<typeof fbResSchema>
export const fbResValidator = getValidator(fbResSchema, dataValidator)
export const fbResResolver = resolve<FbRes, HookContext>({})
export const fbResExternalResolver = resolve<FbRes, HookContext>({})

export const fbResDataSchema = Type.Object({
  ...Type.Omit(fbResSchema, ['_id']).properties
}, { additionalProperties: false })

export type FbResData = Static<typeof fbResDataSchema>
export const fbResDataValidator = getValidator(fbResDataSchema, dataValidator)
export const fbResDataResolver = resolve<FbResData, HookContext>({})

export const fbResPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fbResSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FbResPatch = Static<typeof fbResPatchSchema>
export const fbResPatchValidator = getValidator(fbResPatchSchema, dataValidator)
export const fbResPatchResolver = resolve<FbResPatch, HookContext>({})

// Allow querying on any field from the main schema
const fbResQueryProperties = fbResSchema
export const fbResQuerySchema = querySyntax(fbResQueryProperties)
export type FbResQuery = Static<typeof fbResQuerySchema>
export const fbResQueryValidator = getValidator(fbResQuerySchema, queryValidator)
export const fbResQueryResolver = resolve<FbResQuery, HookContext>({})
