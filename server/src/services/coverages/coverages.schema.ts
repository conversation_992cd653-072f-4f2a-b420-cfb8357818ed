// TypeBox schema for coverages service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, VideoSchema, GeoJsonFeatureSchema, TaxSchema, typeboxToJsonSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Base coverage schema for calculations
const BaseCoverageSchema = Type.Object({
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  deductible: Type.Optional(Type.Number()),
  outOfPocketMax: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  premium: Type.Optional(Type.Number()),
  benefits: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

// Coverage calculation schema (for OpenAI API)
const CoverageCalcSchema = Type.Intersect([
  BaseCoverageSchema,
  Type.Object({
    covered: Type.Union([Type.Literal('individual'), Type.Literal('group')])
  })
])

// Export JSON Schema version for OpenAI API compatibility
export const coverageCalcSchema = typeboxToJsonSchema(CoverageCalcSchema)

// Export additional schemas for backward compatibility
export const fixedRate = Type.Object({
  single: Type.Optional(Type.Number()),
  family: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main coverages schema
export const coveragesSchema = Type.Object({
  _id: ObjectIdSchema(),
  ...BaseCoverageSchema.properties,
  ...commonFields
,
  // Missing fields from old schema
  vectorIds: Type.Optional(ObjectIdSchema()),
  type: Type.Optional(Type.String()),
  uploadIds: Type.Optional(ObjectIdSchema()),
}, { additionalProperties: false })

export type Coverages = Static<typeof coveragesSchema>
export const coveragesValidator = getValidator(coveragesSchema, dataValidator)
export const coveragesResolver = resolve<Coverages, HookContext>({})
export const coveragesExternalResolver = resolve<Coverages, HookContext>({})

// Schema for creating new data
export const coveragesDataSchema = Type.Object({
  ...Type.Omit(coveragesSchema, ['_id']).properties
}, { additionalProperties: false })

export type CoveragesData = Static<typeof coveragesDataSchema>
export const coveragesDataValidator = getValidator(coveragesDataSchema, dataValidator)
export const coveragesDataResolver = resolve<CoveragesData, HookContext>({})

// Schema for updating existing data
export const coveragesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(coveragesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type CoveragesPatch = Static<typeof coveragesPatchSchema>
export const coveragesPatchValidator = getValidator(coveragesPatchSchema, dataValidator)
export const coveragesPatchResolver = resolve<CoveragesPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const coveragesQueryProperties = Type.Object({
  ...Type.Pick(coveragesSchema, ['_id', 'vectorIds', 'uploadIds', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const coveragesQuerySchema = queryWrapper(coveragesQueryProperties)
export type CoveragesQuery = Static<typeof coveragesQuerySchema>
export const coveragesQueryValidator = getValidator(coveragesQuerySchema, queryValidator)
export const coveragesQueryResolver = resolve<CoveragesQuery, HookContext>({})

// Export for backward compatibility
export const coverCopySchema = coveragesSchema
