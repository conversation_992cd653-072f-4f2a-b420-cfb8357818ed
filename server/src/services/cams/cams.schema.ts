// TypeBox schema for cams service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const camsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  url: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  settings: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  person: Type.Optional(ObjectIdSchema()),
  comp: Type.Optional(Type.String()),
  hireDate: Type.Optional(Type.Any()),
}, { additionalProperties: false })

export type Cams = Static<typeof camsSchema>
export const camsValidator = getValidator(camsSchema, dataValidator)
export const camsResolver = resolve<Cams, HookContext>({})
export const camsExternalResolver = resolve<Cams, HookContext>({})

export const camsDataSchema = Type.Object({
  ...Type.Omit(camsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CamsData = Static<typeof camsDataSchema>
export const camsDataValidator = getValidator(camsDataSchema, dataValidator)
export const camsDataResolver = resolve<CamsData, HookContext>({})

export const camsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(camsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
}, { additionalProperties: false })
export type CamsPatch = Static<typeof camsPatchSchema>
export const camsPatchValidator = getValidator(camsPatchSchema, dataValidator)
export const camsPatchResolver = resolve<CamsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const camsQueryProperties = Type.Object({
  ...Type.Pick(camsSchema, ['_id', 'person', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const camsQuerySchema = queryWrapper(camsQueryProperties)
export type CamsQuery = Static<typeof camsQuerySchema>
export const camsQueryValidator = getValidator(camsQuerySchema, queryValidator)
export const camsQueryResolver = resolve<CamsQuery, HookContext>({})
