// TypeBox schema for claim-reqs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const claimReqsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  plan: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  patient: Type.Optional(Type.String()),
  person: Type.Optional(ObjectIdSchema()),
  care: Type.Optional(Type.String()),
  visit: Type.Optional(Type.String()),
  claim: Type.Optional(Type.String()),
  provider: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(Type.String()),
  providerOrg: Type.Optional(Type.String()),
  threads: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type ClaimReqs = Static<typeof claimReqsSchema>
export const claimReqsValidator = getValidator(claimReqsSchema, dataValidator)
export const claimReqsResolver = resolve<ClaimReqs, HookContext>({})
export const claimReqsExternalResolver = resolve<ClaimReqs, HookContext>({})

export const claimReqsDataSchema = Type.Object({
  ...Type.Omit(claimReqsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimReqsData = Static<typeof claimReqsDataSchema>
export const claimReqsDataValidator = getValidator(claimReqsDataSchema, dataValidator)
export const claimReqsDataResolver = resolve<ClaimReqsData, HookContext>({})

export const claimReqsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(claimReqsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ClaimReqsPatch = Static<typeof claimReqsPatchSchema>
export const claimReqsPatchValidator = getValidator(claimReqsPatchSchema, dataValidator)
export const claimReqsPatchResolver = resolve<ClaimReqsPatch, HookContext>({})

// Allow querying on any field from the main schema
const claimReqsQueryProperties = claimReqsSchema
export const claimReqsQuerySchema = querySyntax(claimReqsQueryProperties)
export type ClaimReqsQuery = Static<typeof claimReqsQuerySchema>
export const claimReqsQueryValidator = getValidator(claimReqsQuerySchema, queryValidator)
export const claimReqsQueryResolver = resolve<ClaimReqsQuery, HookContext>({})
