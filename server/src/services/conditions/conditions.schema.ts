// TypeBox schema for conditions service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const conditionsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  codeSystem: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  severity: Type.Optional(Type.String()),
  chronic: Type.Optional(Type.Boolean()),
  symptoms: Type.Optional(Type.Array(Type.String())),
  treatments: Type.Optional(Type.Array(Type.String())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  relatedConditions: Type.Optional(Type.Array(ObjectIdSchema())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  standard: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Conditions = Static<typeof conditionsSchema>
export const conditionsValidator = getValidator(conditionsSchema, dataValidator)
export const conditionsResolver = resolve<Conditions, HookContext>({})
export const conditionsExternalResolver = resolve<Conditions, HookContext>({})

export const conditionsDataSchema = Type.Object({
  ...Type.Omit(conditionsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ConditionsData = Static<typeof conditionsDataSchema>
export const conditionsDataValidator = getValidator(conditionsDataSchema, dataValidator)
export const conditionsDataResolver = resolve<ConditionsData, HookContext>({})

export const conditionsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(conditionsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ConditionsPatch = Static<typeof conditionsPatchSchema>
export const conditionsPatchValidator = getValidator(conditionsPatchSchema, dataValidator)
export const conditionsPatchResolver = resolve<ConditionsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const conditionsQueryProperties = Type.Object({
  ...Type.Pick(conditionsSchema, ['_id', 'medications', 'procedures', 'relatedConditions', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const conditionsQuerySchema = queryWrapper(conditionsQueryProperties)
export type ConditionsQuery = Static<typeof conditionsQuerySchema>
export const conditionsQueryValidator = getValidator(conditionsQuerySchema, queryValidator)
export const conditionsQueryResolver = resolve<ConditionsQuery, HookContext>({})
