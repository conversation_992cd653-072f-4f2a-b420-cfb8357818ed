// TypeBox schema for change-logs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const changeLogsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  service: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type ChangeLogs = Static<typeof changeLogsSchema>
export const changeLogsValidator = getValidator(changeLogsSchema, dataValidator)
export const changeLogsResolver = resolve<ChangeLogs, HookContext>({})
export const changeLogsExternalResolver = resolve<ChangeLogs, HookContext>({})

export const changeLogsDataSchema = Type.Object({
  ...Type.Omit(changeLogsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ChangeLogsData = Static<typeof changeLogsDataSchema>
export const changeLogsDataValidator = getValidator(changeLogsDataSchema, dataValidator)
export const changeLogsDataResolver = resolve<ChangeLogsData, HookContext>({})

export const changeLogsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(changeLogsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ChangeLogsPatch = Static<typeof changeLogsPatchSchema>
export const changeLogsPatchValidator = getValidator(changeLogsPatchSchema, dataValidator)
export const changeLogsPatchResolver = resolve<ChangeLogsPatch, HookContext>({})

// Allow querying on any field from the main schema
const changeLogsQueryProperties = changeLogsSchema
export const changeLogsQuerySchema = querySyntax(changeLogsQueryProperties)
export type ChangeLogsQuery = Static<typeof changeLogsQuerySchema>
export const changeLogsQueryValidator = getValidator(changeLogsQuerySchema, queryValidator)
export const changeLogsQueryResolver = resolve<ChangeLogsQuery, HookContext>({})
