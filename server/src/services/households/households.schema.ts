// TypeBox schema for households service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, addToSet } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const householdsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  head: Type.Optional(ObjectIdSchema()),
  members: Type.Optional(Type.Record(Type.String(), Type.Any())),
  dependents: Type.Optional(Type.Array(ObjectIdSchema())),
  address: Type.Optional(ServiceAddressSchema),
  income: Type.Optional(Type.Number()),
  size: Type.Optional(Type.Number()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  taxFilingStatus: Type.Optional(Type.String()),
  poverty: Type.Optional(Type.Number()),
  fpl: Type.Optional(Type.Number()),
  magi: Type.Optional(Type.Number()),
  aptc: Type.Optional(Type.Number()),
  csr: Type.Optional(Type.String()),
  medicaidEligible: Type.Optional(Type.Boolean()),
  chipEligible: Type.Optional(Type.Boolean()),
  employerCoverage: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  person: Type.Optional(ObjectIdSchema()),
  filingAs: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Households = Static<typeof householdsSchema>
export const householdsValidator = getValidator(householdsSchema, dataValidator)
export const householdsResolver = resolve<Households, HookContext>({})
export const householdsExternalResolver = resolve<Households, HookContext>({})

// Schema for creating new data
export const householdsDataSchema = Type.Object({
  ...Type.Omit(householdsSchema, ['_id']).properties
}, { additionalProperties: false })

export type HouseholdsData = Static<typeof householdsDataSchema>
export const householdsDataValidator = getValidator(householdsDataSchema, dataValidator)
export const householdsDataResolver = resolve<HouseholdsData, HookContext>({})

// Schema for updating existing data
export const householdsPatchSchema = Type.Object({
  name: Type.Optional(Type.String()),
  head: Type.Optional(ObjectIdSchema()),
  members: Type.Optional(Type.Record(Type.String(), Type.Any())),
  dependents: Type.Optional(Type.Array(ObjectIdSchema())),
  address: Type.Optional(ServiceAddressSchema),
  income: Type.Optional(Type.Number()),
  size: Type.Optional(Type.Number()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  taxFilingStatus: Type.Optional(Type.String()),
  poverty: Type.Optional(Type.Number()),
  fpl: Type.Optional(Type.Number()),
  magi: Type.Optional(Type.Number()),
  aptc: Type.Optional(Type.Number()),
  csr: Type.Optional(Type.String()),
  medicaidEligible: Type.Optional(Type.Boolean()),
  chipEligible: Type.Optional(Type.Boolean()),
  employerCoverage: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    dependents: Type.Optional(ObjectIdSchema())
  ,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    dependents: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type HouseholdsPatch = Static<typeof householdsPatchSchema>
export const householdsPatchValidator = getValidator(householdsPatchSchema, dataValidator)
export const householdsPatchResolver = resolve<HouseholdsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const householdsQueryProperties = Type.Object({
  ...Type.Pick(householdsSchema, ['_id', 'head', 'dependents', 'person', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const householdsQuerySchema = querySyntax(householdsQueryProperties)

export type HouseholdsQuery = Static<typeof householdsQuerySchema>
export const householdsQueryValidator = getValidator(householdsQuerySchema, queryValidator)
export const householdsQueryResolver = resolve<HouseholdsQuery, HookContext>({})
