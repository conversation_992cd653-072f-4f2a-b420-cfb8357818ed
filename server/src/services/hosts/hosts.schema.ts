// TypeBox schema for hosts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const hostsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  hostname: Type.Optional(Type.String()),
  ip: Type.Optional(Type.String()),
  port: Type.Optional(Type.Number()),
  protocol: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  lastCheck: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  appDefault: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Hosts = Static<typeof hostsSchema>
export const hostsValidator = getValidator(hostsSchema, dataValidator)
export const hostsResolver = resolve<Hosts, HookContext>({})
export const hostsExternalResolver = resolve<Hosts, HookContext>({})

export const hostsDataSchema = Type.Object({
  ...Type.Omit(hostsSchema, ['_id']).properties
}, { additionalProperties: false })

export type HostsData = Static<typeof hostsDataSchema>
export const hostsDataValidator = getValidator(hostsDataSchema, dataValidator)
export const hostsDataResolver = resolve<HostsData, HookContext>({})

export const hostsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(hostsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type HostsPatch = Static<typeof hostsPatchSchema>
export const hostsPatchValidator = getValidator(hostsPatchSchema, dataValidator)
export const hostsPatchResolver = resolve<HostsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const hostsQueryProperties = Type.Object({
  ...Type.Pick(hostsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const hostsQuerySchema = queryWrapper(hostsQueryProperties)
export type HostsQuery = Static<typeof hostsQuerySchema>
export const hostsQueryValidator = getValidator(hostsQuerySchema, queryValidator)
export const hostsQueryResolver = resolve<HostsQuery, HookContext>({})
