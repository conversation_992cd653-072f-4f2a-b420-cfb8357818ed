// TypeBox schema for bill-erasers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const billErasersSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  person: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  session: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type BillErasers = Static<typeof billErasersSchema>
export const billErasersValidator = getValidator(billErasersSchema, dataValidator)
export const billErasersResolver = resolve<BillErasers, HookContext>({})
export const billErasersExternalResolver = resolve<BillErasers, HookContext>({})

export const billErasersDataSchema = Type.Object({
  ...Type.Omit(billErasersSchema, ['_id']).properties
}, { additionalProperties: false })

export type BillErasersData = Static<typeof billErasersDataSchema>
export const billErasersDataValidator = getValidator(billErasersDataSchema, dataValidator)
export const billErasersDataResolver = resolve<BillErasersData, HookContext>({})

export const billErasersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(billErasersSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $push: Type.Optional(addToSet([])),
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type BillErasersPatch = Static<typeof billErasersPatchSchema>
export const billErasersPatchValidator = getValidator(billErasersPatchSchema, dataValidator)
export const billErasersPatchResolver = resolve<BillErasersPatch, HookContext>({})

// Allow querying on any field from the main schema
const billErasersQueryProperties = billErasersSchema
export const billErasersQuerySchema = queryWrapper(billErasersQueryProperties)
export type BillErasersQuery = Static<typeof billErasersQuerySchema>
export const billErasersQueryValidator = getValidator(billErasersQuerySchema, queryValidator)
export const billErasersQueryResolver = resolve<BillErasersQuery, HookContext>({})
