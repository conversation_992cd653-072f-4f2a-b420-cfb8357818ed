// TypeBox schema for se-plans service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const sePlansSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  org: Type.Optional(ObjectIdSchema()),
  public: Type.Optional(Type.Boolean()),
}, { additionalProperties: false })

export type SePlans = Static<typeof sePlansSchema>
export const sePlansValidator = getValidator(sePlansSchema, dataValidator)
export const sePlansResolver = resolve<SePlans, HookContext>({})
export const sePlansExternalResolver = resolve<SePlans, HookContext>({})

export const sePlansDataSchema = Type.Object({
  ...Type.Omit(sePlansSchema, ['_id']).properties
}, { additionalProperties: false })

export type SePlansData = Static<typeof sePlansDataSchema>
export const sePlansDataValidator = getValidator(sePlansDataSchema, dataValidator)
export const sePlansDataResolver = resolve<SePlansData, HookContext>({})

export const sePlansPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(sePlansSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type SePlansPatch = Static<typeof sePlansPatchSchema>
export const sePlansPatchValidator = getValidator(sePlansPatchSchema, dataValidator)
export const sePlansPatchResolver = resolve<SePlansPatch, HookContext>({})

// Allow querying on any field from the main schema
const sePlansQueryProperties = sePlansSchema
export const sePlansQuerySchema = queryWrapper(sePlansQueryProperties)
export type SePlansQuery = Static<typeof sePlansQuerySchema>
export const sePlansQueryValidator = getValidator(sePlansQuerySchema, queryValidator)
export const sePlansQueryResolver = resolve<SePlansQuery, HookContext>({})
