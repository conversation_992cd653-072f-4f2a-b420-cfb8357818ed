// TypeBox schema for bundles service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const bundlesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  medications: Type.Optional(Type.Array(ObjectIdSchema())),
  services: Type.Optional(Type.Array(Type.String())),
  price: Type.Optional(Type.Number()),
  discount: Type.Optional(Type.Number()),
  discountType: Type.Optional(Type.String()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  conditions: Type.Optional(Type.Array(Type.String())),
  eligibility: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  provider: Type.Optional(ObjectIdSchema()),
  public: Type.Optional(Type.Boolean()),
}, { additionalProperties: false })

export type Bundles = Static<typeof bundlesSchema>
export const bundlesValidator = getValidator(bundlesSchema, dataValidator)
export const bundlesResolver = resolve<Bundles, HookContext>({})
export const bundlesExternalResolver = resolve<Bundles, HookContext>({})

export const bundlesDataSchema = Type.Object({
  ...Type.Omit(bundlesSchema, ['_id']).properties
}, { additionalProperties: false })

export type BundlesData = Static<typeof bundlesDataSchema>
export const bundlesDataValidator = getValidator(bundlesDataSchema, dataValidator)
export const bundlesDataResolver = resolve<BundlesData, HookContext>({})

export const bundlesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(bundlesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type BundlesPatch = Static<typeof bundlesPatchSchema>
export const bundlesPatchValidator = getValidator(bundlesPatchSchema, dataValidator)
export const bundlesPatchResolver = resolve<BundlesPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const bundlesQueryProperties = Type.Object({
  ...Type.Pick(bundlesSchema, ['_id', 'plans', 'coverages', 'procedures', 'medications', 'provider', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const bundlesQuerySchema = querySyntax(bundlesQueryProperties)
export type BundlesQuery = Static<typeof bundlesQuerySchema>
export const bundlesQueryValidator = getValidator(bundlesQuerySchema, queryValidator)
export const bundlesQueryResolver = resolve<BundlesQuery, HookContext>({})
