// TypeBox schema for groups service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const groupsSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  enrollments: Type.Optional(Type.Array(ObjectIdSchema())),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  renewalDate: Type.Optional(Type.Any()),
  minimumParticipation: Type.Optional(Type.Number()),
  currentParticipation: Type.Optional(Type.Number()),
  premiumContribution: Type.Optional(Type.Number()),
  waitingPeriod: Type.Optional(Type.Number()),
  eligibilityRules: Type.Optional(Type.Record(Type.String(), Type.Any())),
  settings: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  key: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Groups = Static<typeof groupsSchema>
export const groupsValidator = getValidator(groupsSchema, dataValidator)
export const groupsResolver = resolve<Groups, HookContext>({})
export const groupsExternalResolver = resolve<Groups, HookContext>({})

// Schema for creating new data
export const groupsDataSchema = Type.Object({
  ...Type.Omit(groupsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GroupsData = Static<typeof groupsDataSchema>
export const groupsDataValidator = getValidator(groupsDataSchema, dataValidator)
export const groupsDataResolver = resolve<GroupsData, HookContext>({})

// Schema for updating existing data
export const groupsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(groupsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })

export type GroupsPatch = Static<typeof groupsPatchSchema>
export const groupsPatchValidator = getValidator(groupsPatchSchema, dataValidator)
export const groupsPatchResolver = resolve<GroupsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const groupsQueryProperties = Type.Object({
  ...Type.Pick(groupsSchema, ['_id', 'org', 'members', 'managers', 'plans', 'coverages', 'enrollments', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const groupsQuerySchema = querySyntax(groupsQueryProperties)

export type GroupsQuery = Static<typeof groupsQuerySchema>
export const groupsQueryValidator = getValidator(groupsQuerySchema, queryValidator)
export const groupsQueryResolver = resolve<GroupsQuery, HookContext>({})
