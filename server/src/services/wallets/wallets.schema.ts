// TypeBox schema for wallets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const walletsSchema = Type.Object({
  _id: ObjectIdSchema(),
  owner: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  currency: Type.Optional(Type.String()),
  balance: Type.Optional(Type.Number()),
  available: Type.Optional(Type.Number()),
  pending: Type.Optional(Type.Number()),
  reserved: Type.Optional(Type.Number()),
  creditLimit: Type.Optional(Type.Number()),
  interestRate: Type.Optional(Type.Number()),
  fees: Type.Optional(Type.Record(Type.String(), Type.Number())),
  transactions: Type.Optional(Type.Array(ObjectIdSchema())),
  linkedAccounts: Type.Optional(Type.Array(ObjectIdSchema())),
  paymentMethods: Type.Optional(Type.Array(ObjectIdSchema())),
  autoReload: Type.Optional(Type.Object({
    enabled: Type.Optional(Type.Boolean()),
    threshold: Type.Optional(Type.Number()),
    amount: Type.Optional(Type.Number()),
    source: Type.Optional(ObjectIdSchema())
  ,
  // Missing fields from old schema
  ownerService: Type.Optional(Type.String()),
}, { additionalProperties: false })),
  limits: Type.Optional(Type.Object({
    daily: Type.Optional(Type.Number()),
    weekly: Type.Optional(Type.Number()),
    monthly: Type.Optional(Type.Number()),
    transaction: Type.Optional(Type.Number())
  }, { additionalProperties: false })),
  status: Type.Optional(Type.String()),
  frozen: Type.Optional(Type.Boolean()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Wallets = Static<typeof walletsSchema>
export const walletsValidator = getValidator(walletsSchema, dataValidator)
export const walletsResolver = resolve<Wallets, HookContext>({})
export const walletsExternalResolver = resolve<Wallets, HookContext>({})

export const walletsDataSchema = Type.Object({
  ...Type.Omit(walletsSchema, ['_id']).properties
}, { additionalProperties: false })

export type WalletsData = Static<typeof walletsDataSchema>
export const walletsDataValidator = getValidator(walletsDataSchema, dataValidator)
export const walletsDataResolver = resolve<WalletsData, HookContext>({})

export const walletsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(walletsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type WalletsPatch = Static<typeof walletsPatchSchema>
export const walletsPatchValidator = getValidator(walletsPatchSchema, dataValidator)
export const walletsPatchResolver = resolve<WalletsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const walletsQueryProperties = Type.Object({
  ...Type.Pick(walletsSchema, ['_id', 'owner', 'transactions', 'linkedAccounts', 'paymentMethods', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const walletsQuerySchema = queryWrapper(walletsQueryProperties)
export type WalletsQuery = Static<typeof walletsQuerySchema>
export const walletsQueryValidator = getValidator(walletsQuerySchema, queryValidator)
export const walletsQueryResolver = resolve<WalletsQuery, HookContext>({})
