// TypeBox schema for sales-taxes service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const salesTaxesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  country: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type SalesTaxes = Static<typeof salesTaxesSchema>
export const salesTaxesValidator = getValidator(salesTaxesSchema, dataValidator)
export const salesTaxesResolver = resolve<SalesTaxes, HookContext>({})
export const salesTaxesExternalResolver = resolve<SalesTaxes, HookContext>({})

export const salesTaxesDataSchema = Type.Object({
  ...Type.Omit(salesTaxesSchema, ['_id']).properties
}, { additionalProperties: false })

export type SalesTaxesData = Static<typeof salesTaxesDataSchema>
export const salesTaxesDataValidator = getValidator(salesTaxesDataSchema, dataValidator)
export const salesTaxesDataResolver = resolve<SalesTaxesData, HookContext>({})

export const salesTaxesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(salesTaxesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type SalesTaxesPatch = Static<typeof salesTaxesPatchSchema>
export const salesTaxesPatchValidator = getValidator(salesTaxesPatchSchema, dataValidator)
export const salesTaxesPatchResolver = resolve<SalesTaxesPatch, HookContext>({})

// Allow querying on any field from the main schema
const salesTaxesQueryProperties = salesTaxesSchema
export const salesTaxesQuerySchema = querySyntax(salesTaxesQueryProperties)
export type SalesTaxesQuery = Static<typeof salesTaxesQuerySchema>
export const salesTaxesQueryValidator = getValidator(salesTaxesQuerySchema, queryValidator)
export const salesTaxesQueryResolver = resolve<SalesTaxesQuery, HookContext>({})
