// TypeBox schema for care-accounts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Last sync schema
const LastSyncSchema = Type.Object({
  adjusted: Type.Optional(Type.Boolean()),
  balance: Type.Optional(Type.Number()),
  date: Type.Optional(Type.Any()),
  err: Type.Optional(Type.String()),
  amount: Type.Optional(Type.Number()),
  recurs: Type.Optional(Type.Number()),
  adjust_spent: Type.Optional(Type.Number()),
  adjust_spent_pending: Type.Optional(Type.Number()),
  adjust_amount: Type.Optional(Type.Number()),
  adjust_assigned_amount: Type.Optional(Type.Number()),
  adjust_recurs: Type.Optional(Type.Number()),
  adjust_assigned_recurs: Type.Optional(Type.Number()),
  freeze: Type.Optional(Type.Boolean()),
  excess: Type.Optional(Type.Number()),
  by: Type.Optional(ObjectIdSchema())
}, { additionalProperties: false })

// Main data model schema
export const careAccountsSchema = Type.Object({
  _id: ObjectIdSchema(),
  amount: Type.Optional(Type.Number()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  assigned_amount: Type.Optional(Type.Number()),
  assigned_recurs: Type.Optional(Type.Number()),
  budgets: Type.Optional(Type.Array(ObjectIdSchema())),
  connect_id: Type.Optional(Type.String()), // deprecated
  stripe_id: Type.Optional(Type.String()), // deprecated
  moov_id: Type.Optional(Type.String()), // moov accountID - treasury.id in orgs
  wallet_id: Type.Optional(Type.String()),
  last4: Type.Optional(Type.String()),
  lastInc: Type.Optional(Type.String()),
  lastSync: Type.Optional(LastSyncSchema),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  name: Type.Optional(Type.String()),
  owner: ObjectIdSchema(),
  recurs: Type.Optional(Type.Number()),
  runSync: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  statusNote: Type.Optional(Type.String()),
  syncHistory: Type.Optional(Type.Array(LastSyncSchema)),
  ...commonFields
}, { additionalProperties: false })

export type CareAccounts = Static<typeof careAccountsSchema>
export const careAccountsValidator = getValidator(careAccountsSchema, dataValidator)
export const careAccountsResolver = resolve<CareAccounts, HookContext>({})
export const careAccountsExternalResolver = resolve<CareAccounts, HookContext>({})

// Schema for creating new data (excluding assigned_amount and assigned_recurs)
export const careAccountsDataSchema = Type.Object({
  ...Type.Omit(careAccountsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CareAccountsData = Static<typeof careAccountsDataSchema>
export const careAccountsDataValidator = getValidator(careAccountsDataSchema, dataValidator)
export const careAccountsDataResolver = resolve<CareAccountsData, HookContext>({})

const listArgs = [
  {path: 'syncHistory', type: LastSyncSchema},
  {path: 'mcc_whitelist', type: Type.String()},
  {path: 'mcc_blacklist', type: Type.String()},
  {path: 'budgets', type: ObjectIdSchema()}
]

// Schema for updating existing data - using commonPatch utility
export const careAccountsPatchSchema = Type.Object({
  amount: Type.Optional(Type.Number()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  assigned_amount: Type.Optional(Type.Number()),
  assigned_recurs: Type.Optional(Type.Number()),
  budgets: Type.Optional(Type.Array(ObjectIdSchema())),
  connect_id: Type.Optional(Type.String()),
  stripe_id: Type.Optional(Type.String()),
  moov_id: Type.Optional(Type.String()),
  wallet_id: Type.Optional(Type.String()),
  last4: Type.Optional(Type.String()),
  lastInc: Type.Optional(Type.String()),
  lastSync: Type.Optional(LastSyncSchema),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  name: Type.Optional(Type.String()),
  owner: Type.Optional(ObjectIdSchema()),
  recurs: Type.Optional(Type.Number()),
  runSync: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  statusNote: Type.Optional(Type.String()),
  syncHistory: Type.Optional(Type.Array(LastSyncSchema)),
  // MongoDB update operators using commonPatch pattern
  ...commonPatch(careAccountsSchema.properties, [{path: 'syncHistory', type: LastSyncSchema}]).properties,
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $addToSet: Type.Optional(Type.Object({
    syncHistory: Type.Optional(LastSyncSchema),
    mcc_whitelist: Type.Optional(Type.String()),
    mcc_blacklist: Type.Optional(Type.String()),
    budgets: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    syncHistory: Type.Optional(LastSyncSchema),
    mcc_whitelist: Type.Optional(Type.String()),
    mcc_blacklist: Type.Optional(Type.String()),
    budgets: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type CareAccountsPatch = Static<typeof careAccountsPatchSchema>
export const careAccountsPatchValidator = getValidator(careAccountsPatchSchema, dataValidator)
export const careAccountsPatchResolver = resolve<CareAccountsPatch, HookContext>({
  owner: async (val) => {
    return undefined // Prevent owner from being updated
  }
})

// Schema for allowed query properties
// Allow querying on any field from the main schema
const careAccountsQueryProperties = careAccountsSchema

export const careAccountsQuerySchema = Type.Intersect([
  queryWrapper(careAccountsQueryProperties),
  Type.Object({
    name: Type.Optional(Type.Any())
  }, { additionalProperties: false })
])

export type CareAccountsQuery = Static<typeof careAccountsQuerySchema>
export const careAccountsQueryValidator = getValidator(careAccountsQuerySchema, queryValidator)
export const careAccountsQueryResolver = resolve<CareAccountsQuery, HookContext>({})
