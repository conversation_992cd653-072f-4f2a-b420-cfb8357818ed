// TypeBox schema for budgets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const budgetsSchema = Type.Object({
  _id: ObjectIdSchema(),
  account: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  amount: Type.Number(),
  spent: Type.Optional(Type.Number()),
  period: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Budgets = Static<typeof budgetsSchema>
export const budgetsValidator = getValidator(budgetsSchema, dataValidator)
export const budgetsResolver = resolve<Budgets, HookContext>({})
export const budgetsExternalResolver = resolve<Budgets, HookContext>({})

export const budgetsDataSchema = Type.Object({
  ...Type.Omit(budgetsSchema, ['_id']).properties
}, { additionalProperties: false })

export type BudgetsData = Static<typeof budgetsDataSchema>
export const budgetsDataValidator = getValidator(budgetsDataSchema, dataValidator)
export const budgetsDataResolver = resolve<BudgetsData, HookContext>({})

export const budgetsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(budgetsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type BudgetsPatch = Static<typeof budgetsPatchSchema>
export const budgetsPatchValidator = getValidator(budgetsPatchSchema, dataValidator)
export const budgetsPatchResolver = resolve<BudgetsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const budgetsQueryProperties = Type.Object({
  ...Type.Pick(budgetsSchema, ['_id', 'account', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const budgetsQuerySchema = queryWrapper(budgetsQueryProperties)
export type BudgetsQuery = Static<typeof budgetsQuerySchema>
export const budgetsQueryValidator = getValidator(budgetsQuerySchema, queryValidator)
export const budgetsQueryResolver = resolve<BudgetsQuery, HookContext>({})
