// TypeBox schema for orgs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, PhoneSchema, ImageSchema, TaxSchema } from '../../utils/common/typebox-schemas.js'

// Treasury schema
const TreasurySchema = Type.Object({
  id: Type.Optional(Type.String()),
  balance: Type.Optional(Type.Number()),
  available: Type.Optional(Type.Number()),
  pending: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main data model schema
export const orgsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  legalName: Type.Optional(Type.String()),
  dba: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  industry: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  website: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  address: Type.Optional(ServiceAddressSchema),
  logo: Type.Optional(ImageSchema),
  ein: Type.Optional(Type.String()),
  taxId: Type.Optional(Type.String()),
  licenses: Type.Optional(Type.Array(Type.String())),
  certifications: Type.Optional(Type.Array(Type.String())),
  parent: Type.Optional(ObjectIdSchema()),
  subsidiaries: Type.Optional(Type.Array(ObjectIdSchema())),
  partners: Type.Optional(Type.Array(ObjectIdSchema())),
  employees: Type.Optional(Type.Array(ObjectIdSchema())),
  owners: Type.Optional(Type.Array(Type.Object({
    id: Type.Optional(ObjectIdSchema()),
    role: Type.Optional(Type.String()),
    percentage: Type.Optional(Type.Number())
  ,
  // Missing fields from old schema
  bankAccounts: Type.Optional(Type.String()),
  patternProperties: Type.Optional(Type.String()),
  properties: Type.Optional(Type.Array(Type.String())),
}, { additionalProperties: false }))),
  groups: Type.Optional(Type.Array(ObjectIdSchema())),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  contracts: Type.Optional(Type.Array(ObjectIdSchema())),
  treasury: Type.Optional(TreasurySchema),
  billing: Type.Optional(Type.Record(Type.String(), Type.Any())),
  settings: Type.Optional(Type.Record(Type.String(), Type.Any())),
  preferences: Type.Optional(Type.Record(Type.String(), Type.Any())),
  taxes: Type.Optional(TaxSchema),
  active: Type.Optional(Type.Boolean()),
  verified: Type.Optional(Type.Boolean()),
  public: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Orgs = Static<typeof orgsSchema>
export const orgsValidator = getValidator(orgsSchema, dataValidator)
export const orgsResolver = resolve<Orgs, HookContext>({})
export const orgsExternalResolver = resolve<Orgs, HookContext>({})

// Schema for creating new data
export const orgsDataSchema = Type.Object({
  ...Type.Omit(orgsSchema, ['_id']).properties
}, { additionalProperties: false })

export type OrgsData = Static<typeof orgsDataSchema>
export const orgsDataValidator = getValidator(orgsDataSchema, dataValidator)
export const orgsDataResolver = resolve<OrgsData, HookContext>({})

// Schema for updating existing data
export const orgsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(orgsSchema, ['_id'])).properties
}, { additionalProperties: false })

export type OrgsPatch = Static<typeof orgsPatchSchema>
export const orgsPatchValidator = getValidator(orgsPatchSchema, dataValidator)
export const orgsPatchResolver = resolve<OrgsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const orgsQueryProperties = Type.Object({
  ...Type.Pick(orgsSchema, ['_id', 'parent', 'subsidiaries', 'partners', 'employees', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const orgsQuerySchema = queryWrapper(orgsQueryProperties)

export type OrgsQuery = Static<typeof orgsQuerySchema>
export const orgsQueryValidator = getValidator(orgsQuerySchema, queryValidator)
export const orgsQueryResolver = resolve<OrgsQuery, HookContext>({})
