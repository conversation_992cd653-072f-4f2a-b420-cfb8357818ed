// TypeBox schema for issues service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const issuesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  service: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Issues = Static<typeof issuesSchema>
export const issuesValidator = getValidator(issuesSchema, dataValidator)
export const issuesResolver = resolve<Issues, HookContext>({})
export const issuesExternalResolver = resolve<Issues, HookContext>({})

export const issuesDataSchema = Type.Object({
  ...Type.Omit(issuesSchema, ['_id']).properties
}, { additionalProperties: false })

export type IssuesData = Static<typeof issuesDataSchema>
export const issuesDataValidator = getValidator(issuesDataSchema, dataValidator)
export const issuesDataResolver = resolve<IssuesData, HookContext>({})

export const issuesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(issuesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type IssuesPatch = Static<typeof issuesPatchSchema>
export const issuesPatchValidator = getValidator(issuesPatchSchema, dataValidator)
export const issuesPatchResolver = resolve<IssuesPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const issuesQueryProperties = Type.Object({
  ...Type.Pick(issuesSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const issuesQuerySchema = queryWrapper(issuesQueryProperties)
export type IssuesQuery = Static<typeof issuesQuerySchema>
export const issuesQueryValidator = getValidator(issuesQuerySchema, queryValidator)
export const issuesQueryResolver = resolve<IssuesQuery, HookContext>({})
