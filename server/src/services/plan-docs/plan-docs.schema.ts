// TypeBox schema for plan-docs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const planDocsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  plan: Type.Optional(ObjectIdSchema()),
  smb: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type PlanDocs = Static<typeof planDocsSchema>
export const planDocsValidator = getValidator(planDocsSchema, dataValidator)
export const planDocsResolver = resolve<PlanDocs, HookContext>({})
export const planDocsExternalResolver = resolve<PlanDocs, HookContext>({})

export const planDocsDataSchema = Type.Object({
  ...Type.Omit(planDocsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PlanDocsData = Static<typeof planDocsDataSchema>
export const planDocsDataValidator = getValidator(planDocsDataSchema, dataValidator)
export const planDocsDataResolver = resolve<PlanDocsData, HookContext>({})

export const planDocsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(planDocsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type PlanDocsPatch = Static<typeof planDocsPatchSchema>
export const planDocsPatchValidator = getValidator(planDocsPatchSchema, dataValidator)
export const planDocsPatchResolver = resolve<PlanDocsPatch, HookContext>({})

// Allow querying on any field from the main schema
const planDocsQueryProperties = planDocsSchema
export const planDocsQuerySchema = queryWrapper(planDocsQueryProperties)
export type PlanDocsQuery = Static<typeof planDocsQuerySchema>
export const planDocsQueryValidator = getValidator(planDocsQuerySchema, queryValidator)
export const planDocsQueryResolver = resolve<PlanDocsQuery, HookContext>({})
