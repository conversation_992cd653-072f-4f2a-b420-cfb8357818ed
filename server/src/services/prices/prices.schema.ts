// TypeBox schema for prices service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const pricesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  amount: Type.Number(),
  currency: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  effectiveDate: Type.Optional(Type.Any()),
  expirationDate: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  provider: Type.Optional(ObjectIdSchema()),
  bundle: Type.Optional(Type.String()),
  source: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Prices = Static<typeof pricesSchema>
export const pricesValidator = getValidator(pricesSchema, dataValidator)
export const pricesResolver = resolve<Prices, HookContext>({})
export const pricesExternalResolver = resolve<Prices, HookContext>({})

export const pricesDataSchema = Type.Object({
  ...Type.Omit(pricesSchema, ['_id']).properties
}, { additionalProperties: false })

export type PricesData = Static<typeof pricesDataSchema>
export const pricesDataValidator = getValidator(pricesDataSchema, dataValidator)
export const pricesDataResolver = resolve<PricesData, HookContext>({})

export const pricesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(pricesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $push: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type PricesPatch = Static<typeof pricesPatchSchema>
export const pricesPatchValidator = getValidator(pricesPatchSchema, dataValidator)
export const pricesPatchResolver = resolve<PricesPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const pricesQueryProperties = Type.Object({
  ...Type.Pick(pricesSchema, ['_id', 'provider', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const pricesQuerySchema = queryWrapper(pricesQueryProperties)
export type PricesQuery = Static<typeof pricesQuerySchema>
export const pricesQueryValidator = getValidator(pricesQuerySchema, queryValidator)
export const pricesQueryResolver = resolve<PricesQuery, HookContext>({})
