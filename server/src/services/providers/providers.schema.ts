// TypeBox schema for providers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ImageSchema, VideoSchema, ServiceAddressSchema, GeoJsonFeatureSchema, addToSet, pull } from '../../utils/common/typebox-schemas.js'

// Day time schema
const DaySchema = Type.Object({
  hour: Type.Optional(Type.Number()),
  minute: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Open day schema
const OpenDaySchema = Type.Object({
  open: Type.Optional(DaySchema),
  close: Type.Optional(DaySchema)
}, { additionalProperties: false })

// Opening hours schema (pattern properties for days 0-6)
const OpenHoursSchema = Type.Record(
  Type.String({ pattern: '^[0-6]$' }),
  OpenDaySchema,
  { additionalProperties: false }
)

// Main data model schema
export const providersSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: Type.Optional(ObjectIdSchema()),
  avatar: Type.Optional(ImageSchema),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  phones: Type.Optional(Type.Array(PhoneSchema)),
  email: Type.Optional(Type.String()),
  emails: Type.Optional(Type.Array(Type.String())),
  website: Type.Optional(Type.String()),
  address: Type.Optional(ServiceAddressSchema),
  addresses: Type.Optional(Type.Array(ServiceAddressSchema)),
  openHours: Type.Optional(OpenHoursSchema),
  timezone: Type.Optional(Type.String()),
  video: Type.Optional(VideoSchema),
  npi: Type.Optional(Type.String()),
  taxId: Type.Optional(Type.String()),
  taxonomy: Type.Optional(Type.String()),
  taxonomy1: Type.Optional(Type.String()),
  taxonomy2: Type.Optional(Type.String()),
  taxonomy3: Type.Optional(Type.String()),
  specialties: Type.Optional(Type.Array(Type.String())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  locations: Type.Optional(Type.Array(ObjectIdSchema())),
  services: Type.Optional(Type.Array(Type.String())),
  features: Type.Optional(Type.Array(GeoJsonFeatureSchema)),
  public: Type.Optional(Type.Boolean()),
  verified: Type.Optional(Type.Boolean()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Providers = Static<typeof providersSchema>
export const providersValidator = getValidator(providersSchema, dataValidator)
export const providersResolver = resolve<Providers, HookContext>({})
export const providersExternalResolver = resolve<Providers, HookContext>({})

// Schema for creating new data
export const providersDataSchema = Type.Object({
  ...Type.Omit(providersSchema, ['_id']).properties
}, { additionalProperties: false })

export type ProvidersData = Static<typeof providersDataSchema>
export const providersDataValidator = getValidator(providersDataSchema, dataValidator)
export const providersDataResolver = resolve<ProvidersData, HookContext>({})

// Schema for updating existing data
export const providersPatchSchema = Type.Object({
  org: Type.Optional(ObjectIdSchema()),
  avatar: Type.Optional(ImageSchema),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  phones: Type.Optional(Type.Array(PhoneSchema)),
  email: Type.Optional(Type.String()),
  emails: Type.Optional(Type.Array(Type.String())),
  website: Type.Optional(Type.String()),
  address: Type.Optional(ServiceAddressSchema),
  addresses: Type.Optional(Type.Array(ServiceAddressSchema)),
  openHours: Type.Optional(OpenHoursSchema),
  timezone: Type.Optional(Type.String()),
  video: Type.Optional(VideoSchema),
  npi: Type.Optional(Type.String()),
  taxId: Type.Optional(Type.String()),
  taxonomy: Type.Optional(Type.String()),
  taxonomy1: Type.Optional(Type.String()),
  taxonomy2: Type.Optional(Type.String()),
  taxonomy3: Type.Optional(Type.String()),
  specialties: Type.Optional(Type.Array(Type.String())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  locations: Type.Optional(Type.Array(ObjectIdSchema())),
  services: Type.Optional(Type.Array(Type.String())),
  features: Type.Optional(Type.Array(GeoJsonFeatureSchema)),
  public: Type.Optional(Type.Boolean()),
  verified: Type.Optional(Type.Boolean()),
  active: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    networks: Type.Optional(ObjectIdSchema()),
    practitioners: Type.Optional(ObjectIdSchema()),
    locations: Type.Optional(ObjectIdSchema()),
    services: Type.Optional(Type.String()),
    features: Type.Optional(GeoJsonFeatureSchema)
  ,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    networks: Type.Optional(ObjectIdSchema()),
    practitioners: Type.Optional(ObjectIdSchema()),
    locations: Type.Optional(ObjectIdSchema()),
    services: Type.Optional(Type.String()),
    features: Type.Optional(GeoJsonFeatureSchema)
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type ProvidersPatch = Static<typeof providersPatchSchema>
export const providersPatchValidator = getValidator(providersPatchSchema, dataValidator)
export const providersPatchResolver = resolve<ProvidersPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const providersQueryProperties = Type.Object({
  ...Type.Pick(providersSchema, ['_id', 'org', 'networks', 'practitioners', 'locations', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const providersQuerySchema = Type.Intersect([
  querySyntax(providersQueryProperties),
  Type.Object({
    name: Type.Optional(Type.Any()),
    'address.city': Type.Optional(Type.String()),
    'address.region': Type.Optional(Type.String())
  }, { additionalProperties: false })
])

export type ProvidersQuery = Static<typeof providersQuerySchema>
export const providersQueryValidator = getValidator(providersQuerySchema, queryValidator)
export const providersQueryResolver = resolve<ProvidersQuery, HookContext>({})
