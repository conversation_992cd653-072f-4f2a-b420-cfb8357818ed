// TypeBox schema for bank-accounts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const bankAccountsSchema = Type.Object({
  _id: ObjectIdSchema(),
  owner: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  accountNumber: Type.Optional(Type.String()),
  routingNumber: Type.Optional(Type.String()),
  accountType: Type.Optional(Type.String()),
  bankName: Type.Optional(Type.String()),
  verified: Type.Optional(Type.Boolean()),
  primary: Type.Optional(Type.Boolean()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  nickname: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type BankAccounts = Static<typeof bankAccountsSchema>
export const bankAccountsValidator = getValidator(bankAccountsSchema, dataValidator)
export const bankAccountsResolver = resolve<BankAccounts, HookContext>({})
export const bankAccountsExternalResolver = resolve<BankAccounts, HookContext>({})

export const bankAccountsDataSchema = Type.Object({
  ...Type.Omit(bankAccountsSchema, ['_id']).properties
}, { additionalProperties: false })

export type BankAccountsData = Static<typeof bankAccountsDataSchema>
export const bankAccountsDataValidator = getValidator(bankAccountsDataSchema, dataValidator)
export const bankAccountsDataResolver = resolve<BankAccountsData, HookContext>({})

export const bankAccountsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(bankAccountsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type BankAccountsPatch = Static<typeof bankAccountsPatchSchema>
export const bankAccountsPatchValidator = getValidator(bankAccountsPatchSchema, dataValidator)
export const bankAccountsPatchResolver = resolve<BankAccountsPatch, HookContext>({})

// Allow querying on any field from the main schema
const bankAccountsQueryProperties = bankAccountsSchema
export const bankAccountsQuerySchema = queryWrapper(bankAccountsQueryProperties)
export type BankAccountsQuery = Static<typeof bankAccountsQuerySchema>
export const bankAccountsQueryValidator = getValidator(bankAccountsQuerySchema, queryValidator)
export const bankAccountsQueryResolver = resolve<BankAccountsQuery, HookContext>({})
