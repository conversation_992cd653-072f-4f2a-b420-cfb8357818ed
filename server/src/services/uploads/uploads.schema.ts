// TypeBox schema for uploads service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const uploadsSchema = Type.Object({
  _id: ObjectIdSchema(),
  filename: Type.String(),
  originalName: Type.Optional(Type.String()),
  mimetype: Type.Optional(Type.String()),
  size: Type.Optional(Type.Number()),
  path: Type.Optional(Type.String()),
  url: Type.Optional(Type.String()),
  storage: Type.Optional(Type.String()),
  bucket: Type.Optional(Type.String()),
  key: Type.Optional(Type.String()),
  uploadedBy: Type.Optional(ObjectIdSchema()),
  uploadedAt: Type.Optional(Type.Any()),
  tags: Type.Optional(Type.Array(Type.String())),
  metadata: Type.Optional(Type.Record(Type.String(), Type.Any())),
  processed: Type.Optional(Type.Boolean()),
  virus_scan: Type.Optional(Type.Object({
    status: Type.Optional(Type.String()),
    result: Type.Optional(Type.String()),
    scannedAt: Type.Optional(Type.Any())
  ,
  // Missing fields from old schema
  name: Type.Optional(Type.String()),
}, { additionalProperties: false })),
  access: Type.Optional(Type.String()),
  permissions: Type.Optional(Type.Record(Type.String(), Type.Any())),
  expiresAt: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Uploads = Static<typeof uploadsSchema>
export const uploadsValidator = getValidator(uploadsSchema, dataValidator)
export const uploadsResolver = resolve<Uploads, HookContext>({})
export const uploadsExternalResolver = resolve<Uploads, HookContext>({})

export const uploadsDataSchema = Type.Object({
  ...Type.Omit(uploadsSchema, ['_id']).properties
}, { additionalProperties: false })

export type UploadsData = Static<typeof uploadsDataSchema>
export const uploadsDataValidator = getValidator(uploadsDataSchema, dataValidator)
export const uploadsDataResolver = resolve<UploadsData, HookContext>({})

export const uploadsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(uploadsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type UploadsPatch = Static<typeof uploadsPatchSchema>
export const uploadsPatchValidator = getValidator(uploadsPatchSchema, dataValidator)
export const uploadsPatchResolver = resolve<UploadsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const uploadsQueryProperties = Type.Object({
  ...Type.Pick(uploadsSchema, ['_id', 'uploadedBy', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const uploadsQuerySchema = queryWrapper(uploadsQueryProperties)
export type UploadsQuery = Static<typeof uploadsQuerySchema>
export const uploadsQueryValidator = getValidator(uploadsQuerySchema, queryValidator)
export const uploadsQueryResolver = resolve<UploadsQuery, HookContext>({})
