// TypeBox schema for cares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull } from '../../utils/common/typebox-schemas.js'

export const caresSchema = Type.Object({
  _id: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  provider: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  priority: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  startDate: Type.Optional(Type.Any()),
  endDate: Type.Optional(Type.Any()),
  goals: Type.Optional(Type.Array(Type.String())),
  interventions: Type.Optional(Type.Array(Type.String())),
  outcomes: Type.Optional(Type.Array(Type.String())),
  notes: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  person: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
}, { additionalProperties: false })

export type Cares = Static<typeof caresSchema>
export const caresValidator = getValidator(caresSchema, dataValidator)
export const caresResolver = resolve<Cares, HookContext>({})
export const caresExternalResolver = resolve<Cares, HookContext>({})

export const caresDataSchema = Type.Object({
  ...Type.Omit(caresSchema, ['_id']).properties
}, { additionalProperties: false })

export type CaresData = Static<typeof caresDataSchema>
export const caresDataValidator = getValidator(caresDataSchema, dataValidator)
export const caresDataResolver = resolve<CaresData, HookContext>({})

export const caresPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(caresSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type CaresPatch = Static<typeof caresPatchSchema>
export const caresPatchValidator = getValidator(caresPatchSchema, dataValidator)
export const caresPatchResolver = resolve<CaresPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const caresQueryProperties = Type.Object({
  ...Type.Pick(caresSchema, ['_id', 'patient', 'provider', 'practitioner', 'person', 'org', 'plan', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const caresQuerySchema = queryWrapper(caresQueryProperties)
export type CaresQuery = Static<typeof caresQuerySchema>
export const caresQueryValidator = getValidator(caresQuerySchema, queryValidator)
export const caresQueryResolver = resolve<CaresQuery, HookContext>({})
