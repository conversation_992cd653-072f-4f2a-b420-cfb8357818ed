// TypeBox schema for funds-requests service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const fundsRequestsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type FundsRequests = Static<typeof fundsRequestsSchema>
export const fundsRequestsValidator = getValidator(fundsRequestsSchema, dataValidator)
export const fundsRequestsResolver = resolve<FundsRequests, HookContext>({})
export const fundsRequestsExternalResolver = resolve<FundsRequests, HookContext>({})

export const fundsRequestsDataSchema = Type.Object({
  ...Type.Omit(fundsRequestsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FundsRequestsData = Static<typeof fundsRequestsDataSchema>
export const fundsRequestsDataValidator = getValidator(fundsRequestsDataSchema, dataValidator)
export const fundsRequestsDataResolver = resolve<FundsRequestsData, HookContext>({})

export const fundsRequestsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fundsRequestsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FundsRequestsPatch = Static<typeof fundsRequestsPatchSchema>
export const fundsRequestsPatchValidator = getValidator(fundsRequestsPatchSchema, dataValidator)
export const fundsRequestsPatchResolver = resolve<FundsRequestsPatch, HookContext>({})

// Allow querying on any field from the main schema
const fundsRequestsQueryProperties = fundsRequestsSchema
export const fundsRequestsQuerySchema = querySyntax(fundsRequestsQueryProperties)
export type FundsRequestsQuery = Static<typeof fundsRequestsQuerySchema>
export const fundsRequestsQueryValidator = getValidator(fundsRequestsQuerySchema, queryValidator)
export const fundsRequestsQueryResolver = resolve<FundsRequestsQuery, HookContext>({})
