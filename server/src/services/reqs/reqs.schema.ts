// TypeBox schema for reqs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields } from '../../utils/common/typebox-schemas.js'

export const reqsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  fingerprint: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Reqs = Static<typeof reqsSchema>
export const reqsValidator = getValidator(reqsSchema, dataValidator)
export const reqsResolver = resolve<Reqs, HookContext>({})
export const reqsExternalResolver = resolve<Reqs, HookContext>({})

export const reqsDataSchema = Type.Object({
  ...Type.Omit(reqsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ReqsData = Static<typeof reqsDataSchema>
export const reqsDataValidator = getValidator(reqsDataSchema, dataValidator)
export const reqsDataResolver = resolve<ReqsData, HookContext>({})

export const reqsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(reqsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ReqsPatch = Static<typeof reqsPatchSchema>
export const reqsPatchValidator = getValidator(reqsPatchSchema, dataValidator)
export const reqsPatchResolver = resolve<ReqsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const reqsQueryProperties = Type.Object({
  ...Type.Pick(reqsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const reqsQuerySchema = querySyntax(reqsQueryProperties)
export type ReqsQuery = Static<typeof reqsQuerySchema>
export const reqsQueryValidator = getValidator(reqsQuerySchema, queryValidator)
export const reqsQueryResolver = resolve<ReqsQuery, HookContext>({})
