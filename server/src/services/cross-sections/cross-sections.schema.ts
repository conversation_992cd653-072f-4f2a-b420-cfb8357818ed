// TypeBox schema for cross-sections service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema, queryWrapper } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet } from '../../utils/common/typebox-schemas.js'

export const crossSectionsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  hackId: Type.Optional(ObjectIdSchema()),
}, { additionalProperties: false })

export type CrossSections = Static<typeof crossSectionsSchema>
export const crossSectionsValidator = getValidator(crossSectionsSchema, dataValidator)
export const crossSectionsResolver = resolve<CrossSections, HookContext>({})
export const crossSectionsExternalResolver = resolve<CrossSections, HookContext>({})

export const crossSectionsDataSchema = Type.Object({
  ...Type.Omit(crossSectionsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CrossSectionsData = Static<typeof crossSectionsDataSchema>
export const crossSectionsDataValidator = getValidator(crossSectionsDataSchema, dataValidator)
export const crossSectionsDataResolver = resolve<CrossSectionsData, HookContext>({})

export const crossSectionsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(crossSectionsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type CrossSectionsPatch = Static<typeof crossSectionsPatchSchema>
export const crossSectionsPatchValidator = getValidator(crossSectionsPatchSchema, dataValidator)
export const crossSectionsPatchResolver = resolve<CrossSectionsPatch, HookContext>({})

// Allow querying on any field from the main schema
const crossSectionsQueryProperties = crossSectionsSchema
export const crossSectionsQuerySchema = queryWrapper(crossSectionsQueryProperties)
export type CrossSectionsQuery = Static<typeof crossSectionsQuerySchema>
export const crossSectionsQueryValidator = getValidator(crossSectionsQuerySchema, queryValidator)
export const crossSectionsQueryResolver = resolve<CrossSectionsQuery, HookContext>({})
