import { Type, queryWrapper } from '@feathersjs/typebox';

// Simulate the logins query properties
const testQueryProperties = Type.Object({
  _id: Type.String(),
  person: Type.String(),
  did: Type.Optional(Type.String())
}, { additionalProperties: true });

const testQuerySchema = queryWrapper(testQueryProperties);

console.log('Query Properties Schema:');
console.log(JSON.stringify(testQueryProperties, null, 2));
console.log('\nQuery Schema after queryWrapper:');
console.log(JSON.stringify(testQuerySchema, null, 2));

// Test validation
import { getValidator } from '@feathersjs/typebox';

const validator = getValidator(testQuerySchema);

// Test query with 'did' field
const testQuery = { did: 'some-value' };
console.log('\nTesting query with did field:', testQuery);

try {
  const result = validator(testQuery);
  console.log('Validation result:', result);
} catch (error) {
  console.log('Validation error:', error.message);
  console.log('Error details:', error);
}
